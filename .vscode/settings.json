{"cSpell.diagnosticLevel": "Hint", "cSpell.words": ["ACTIVEPIECES", "AISDK", "AISO", "Antd", "apitable", "appspot", "automations", "basenext", "bika", "<PERSON>r", "<PERSON><PERSON><PERSON>", "datasheets", "Datasource", "DATERANGE", "datetime", "DEEPSEEK", "<PERSON><PERSON><PERSON>", "estypes", "etags", "exceljs", "<PERSON><PERSON><PERSON>", "firebaseapp", "formapp", "langchain", "mailparser", "minio", "Mobilephone", "msgtype", "nextjs", "NOSELECT", "olap", "openai", "Pkce", "reddot", "reddots", "SDKAI", "SDKAISO", "seqno", "sharelib", "<PERSON><PERSON>", "Skillset", "skillsets", "TAVILY", "tencentcloud", "todos", "TOOLAPP", "toolkits", "toolsdk", "Toolsets", "trpc", "uids", "updator", "vercel", "vika", "vikadata", "Voov", "webpush", "webset", "Websets", "WECHAT", "Wecom"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "files.watcherExclude": {"**/.git/objects/**": true, "**/node_modules/**": true}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "CodeGPT.apiKey": "Google AI Studio"}