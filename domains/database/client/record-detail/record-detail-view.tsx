'use client';

import { useTR<PERSON>Query } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { SpaceModalConfig } from '@bika/domains/space/client/modals/space-modals-context';
import { useDatabaseVOContext, useLookupContext } from '@bika/types/database/context';
import type { RecordCreate, RecordUpdate, RecordUpdateDTO } from '@bika/types/database/dto';
import type { DatabaseLookupFieldRO } from '@bika/types/database/ro';
import type {
  DatabaseVO,
  ILookupVO,
  RecordDetailVO,
  RecordRenderVO,
  ViewVO,
} from '@bika/types/database/vo';
import type { MirrorVO } from '@bika/types/node/vo';
import {
  useShareContext,
  useSpaceContextForce,
  useSpaceId,
  useSpaceRouter,
} from '@bika/types/space/context';
import type { RemoteStoragePropertyDatabaseViewFilter } from '@bika/types/system/remote-storage';
import { But<PERSON> } from '@bika/ui/button-component';
import { Empty } from '@bika/ui/components/empty/index';
import { getRecordDTOFromRecordDetailVO } from '@bika/ui/form/utils';
import { Box, Stack } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { useStackHeaderBar } from '@bika/ui/stack-header-bar';
import { useStackNavigatorContext } from '@bika/ui/stack-navigator';
import { FooterButtons } from '@bika/ui/web-layout';
import { useDeepCompareEffect, useLocalStorageState } from 'ahooks';
import assert from 'assert';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useSessionStorage } from 'react-use';
import { useRemoteStorage } from '../../../system/client/hooks/useRemoteStorage';
import { DatabaseVOProviderWithApi } from '../context/database-vo-provider-with-api';
import { TypesFormInputConfig } from '../form/config';
import { RecordActivityView } from '../record-activity/record-activity-view';
import { useCurrentUser } from '../table-view/use-current-user';
import { extractInitialCellValuesFromViewFilter } from './extract-initial-cell-values-from-view-filter';
import { getParams } from './getParams';
import type { IRecordDetailParams, RecordDetailControlStatus } from './interface';
import { RecordDetailToolbarComponent } from './record-detail-toolbar-component';
import { RecordDetailVOEditor } from './record-detail-vo-editor';
import { useLookupRecordEditorSync } from './use-lookup-record-editor-sync';
import { useRecordDetailNavigation } from './use-record-detail-navigation';
import { CONST_INFINITE_KEY, renderRecordVO } from './utils';

export function generateStorageKey(
  props: IRecordDetailParams<RecordDetailControlStatus>,
  uniqueId: number,
): string {
  return `CONST_RECORD_DETAIL_${props?.targetRecordId ?? ''}_${props?.targetFieldId ?? ''}_${props?.nodeId ?? ''}_${uniqueId}_${props.recordId ?? ''}`;
}

interface Props extends IRecordDetailParams<RecordDetailControlStatus> {
  onSuccess?: (_data: RecordRenderVO, reason: 'CREATE' | 'UPDATE') => void;
  onCancel?: () => void;
  onClose?: () => void;
}

const getInitialStatus = (initialStatus?: RecordDetailControlStatus, recordId?: string) =>
  initialStatus ?? (!recordId ? 'CREATE' : 'VIEW');

function RecordDetailViewInner(props: Props) {
  const trpcQuery = useTRPCQuery();
  // const frameworkContext = useUIFrameworkContext();
  const router = useSpaceRouter();
  const spaceId = useSpaceId();
  const locale = useLocale();

  const { value: databaseVO, useRecordMutation, getRecord } = useDatabaseVOContext();
  const { createRecord, updateRecord, updateMutating, isMutating } = useRecordMutation();

  // TODO 坏味道 后台冗余接口?
  //
  const { data: fields } = trpcQuery.database.getFieldROs.useQuery(
    {
      databaseId: props.nodeId as string,
    },
    {
      enabled: props.nodeId !== undefined,
      meta: { skipError: true },
    },
  );
  const viewData1 = useMemo(() => {
    if (!databaseVO) {
      return null;
    }
    if (!props.viewId) {
      return databaseVO?.views?.[0];
    }
    return databaseVO?.views.find((view) => view.id === props.viewId);
  }, [databaseVO, props.viewId]);

  const viewData: ViewVO | null = useMemo(() => {
    if (!viewData1) {
      return null;
    }
    if (!fields) {
      return viewData1;
    }

    const newFields = viewData1.columns
      .map((field) => {
        const foundField = fields.find((_field) => _field.id === field.id);
        if (!foundField) {
          return field;
        }
        return {
          ...field,
          property: foundField?.property ?? field.property,
          render: (foundField as DatabaseLookupFieldRO)?.render,
        };
      })
      .filter(Boolean);

    return {
      ...viewData1,
      columns: newFields,
    } as ViewVO;
  }, [viewData1, fields]);

  const ctx = useStackNavigatorContext();

  const stackParams = getParams<{
    mode?: RecordDetailControlStatus;
  }>(ctx.params);

  const status = getInitialStatus(stackParams.mode, props.recordId);

  const recordDetailNavigationApi = useRecordDetailNavigation();

  const setStatus = useCallback(
    (mode: RecordDetailControlStatus) => {
      recordDetailNavigationApi.replace({
        mode,
      });
    },
    [recordDetailNavigationApi],
  );

  const routerParams = router?.useParams<{
    nodeId: string;
    viewId: string;
  }>();

  useEffect(() => {
    if (props.mode) {
      const newStatus = getInitialStatus(props.mode, props.recordId);
      setStatus(newStatus);
    }
    // DONT add setStatus as dependency
  }, [props.mode, props.recordId]);

  useEffect(() => {
    if (status === 'CREATE') {
      assert(props.nodeId, 'databaseId is required in CREATE mode');
    }
  }, [props.nodeId, status]);

  const columns = useMemo(() => viewData?.columns ?? [], [viewData]);

  const { toast } = useSnackBar();

  const { uniqueId } = useLookupContext();
  const constStorageKey = generateStorageKey(props, uniqueId);

  const [_recordDetail, setRecordDetail] = useSessionStorage(constStorageKey);

  const auth = useCurrentUser();

  const recordDetail = _recordDetail as RecordDetailVO | null;

  const [tick, setTick] = useState(0);

  const { myInfo } = useSpaceContextForce();

  const handleFromStateReset = () => {
    if (props.recordId) {
      setRecordDetail(undefined);
      setTick((p) => p + 1);
      utils.database.getRecord.invalidate({
        databaseId: props.nodeId,
        recordId: props.recordId,
      });
    }
  };
  const handleCancel = () => {
    if (status === 'UPDATE') {
      setStatus('VIEW');
      handleFromStateReset();
      return;
    }
    props?.onCancel?.();
  };

  // Record VO
  const {
    data: fetchedRecordDetailVO,
    isFetching,
    isLoading,
    isError,
    isSuccess,
  } = trpcQuery.database.getRecord.useQuery(
    {
      databaseId: props.nodeId ?? '',
      mirrorId: props.mirrorId,
      recordId: props.recordId ?? '',
    },
    {
      enabled: Boolean(props.nodeId) && Boolean(props.recordId),
    },
  );

  useEffect(() => {
    if (isSuccess) {
      setStatus(props?.mode ?? 'VIEW');
    }
  }, [isSuccess, setStatus, props?.mode]);

  // 个人用户的filter信息，从远程获取
  const [filterData] = useRemoteStorage<RemoteStoragePropertyDatabaseViewFilter>(
    'DATABASE_VIEW_FILTER',
    props.viewId ?? '',
  );

  useDeepCompareEffect(() => {
    const gotStorageItem = sessionStorage.getItem(constStorageKey);
    if (gotStorageItem != null && gotStorageItem !== 'undefined') {
      try {
        const data = JSON.parse(gotStorageItem);
        setRecordDetail(data);
        return;
      } catch (_e) {
        console.error('recover data');
      }
    }
    if (props.recordId && !fetchedRecordDetailVO) {
      return;
    }

    if (props.viewId && !viewData) {
      return;
    }

    let isFieldReady = fields && fields.length > 0;
    if (props.mirrorId) {
      isFieldReady = true;
    }
    if (!isLoading && !isFetching && fetchedRecordDetailVO && viewData && isFieldReady) {
      if (fetchedRecordDetailVO?.record?.databaseId !== props.nodeId) {
        return;
      }
      if (viewData.databaseId !== props.nodeId) {
        return;
      }
      setRecordDetail({
        ...fetchedRecordDetailVO,
        fields: columns,
      } as RecordDetailVO);
      return;
    }

    if (fetchedRecordDetailVO && !props.viewId) {
      if (fetchedRecordDetailVO?.record?.databaseId !== props.nodeId) {
        return;
      }
      // 如果没有视图 Id 则要求 有fetchedRecordDetailVO
      setRecordDetail({
        ...fetchedRecordDetailVO,
        fields: columns,
      } as RecordDetailVO);
      return;
    }

    if (props.recordId && isLoading) {
      return;
    }
    if (props.recordId && isFetching) {
      return;
    }

    const initialCellValues = viewData
      ? extractInitialCellValuesFromViewFilter(viewData, {
          currentUserId: myInfo?.id ?? '',
        })
      : {};

    const initialCellValueFromCustomFilter =
      routerParams.viewId === props.viewId && routerParams.nodeId === props.nodeId && filterData
        ? extractInitialCellValuesFromViewFilter(
            {
              filters: filterData.filters,
              columns: viewData?.columns ?? [],
            },
            {
              currentUserId: myInfo?.id ?? '',
            },
          )
        : {};

    // Create new record data with initial values from view filters or defaults
    const newRecordData = {
      fields: columns,
      record: {
        id: '',
        databaseId: props.nodeId,
        revision: 0,
        cells: {
          ...TypesFormInputConfig.getInitialValue(columns),
          ...initialCellValues,
          ...initialCellValueFromCustomFilter,
        },
      },
      revision: -1,
    } as RecordDetailVO;
    setRecordDetail(newRecordData);
  }, [columns, tick, fetchedRecordDetailVO, isLoading, isFetching, props.nodeId, props.recordId]);

  const refresh = useLookupRecordEditorSync(props.recordId ?? '', setRecordDetail);

  const stackHeaderBarCtx = useStackHeaderBar();
  const utils = trpcQuery.useUtils();

  const { appendLookupValue } = useLookupContext();

  useEffect(() => {
    stackHeaderBarCtx?.setShowHeader(false);
  }, [stackHeaderBarCtx]);

  const onSuccess = useCallback(
    async (resp: RecordRenderVO, lookupResult: ILookupVO, reason: 'CREATE' | 'UPDATE') => {
      if (reason === 'CREATE') {
        if (props.targetFieldId && props.targetRecordId != null) {
          appendLookupValue(props.targetRecordId, props.targetFieldId, lookupResult);
        }

        toast(t.editor.operate_successfully, {
          variant: 'success',
          action: {
            text: t.action.view,
            onClick: () => {
              router.push(`/space/${spaceId}/node/${props.nodeId}/${props.viewId}/${resp.id}`);
            },
          },
        });
        setRecordDetail(undefined);
      } else {
        toast(t.editor.operate_successfully, {
          variant: 'success',
        });
        const da = await getRecord({
          databaseId: props.nodeId ?? '',
          mirrorId: props.mirrorId,
          recordId: props.recordId ?? '',
        });
        setRecordDetail(da);
      }

      await utils.database.listRecords.invalidate({
        databaseId: resp.databaseId,
      });

      await utils.database.getInfiniteRecords.invalidate(
        {
          databaseId: resp.databaseId,
          limit: CONST_INFINITE_KEY,
        },
        {
          type: 'all',
        },
      );

      setStatus('VIEW');
      await utils.database.getRecord.invalidate({
        databaseId: resp.databaseId,
        recordId: resp.id,
      });
      props.onSuccess?.(resp, reason);
    },
    [
      spaceId,
      appendLookupValue,
      props,
      router,
      setStatus,
      status,
      toast,
      utils.database.getRecord,
      utils.database.listRecords,
      utils.database.getInfiniteRecords,
    ],
  );

  const handleSubmit = useCallback(async () => {
    if (!recordDetail) {
      return;
    }
    let recordDto: RecordCreate | RecordUpdate | null = null;
    try {
      recordDto = getRecordDTOFromRecordDetailVO(recordDetail, true, locale);
    } catch (e) {
      toast((e as unknown as Error).message, {
        variant: 'error',
      });
    }
    if (!recordDto) {
      return;
    }
    if (!props.nodeId) {
      return;
    }
    if (status === 'CREATE') {
      const res: RecordRenderVO = await createRecord?.({
        ...recordDto,
        databaseId: props.nodeId,
        mirrorId: props.mirrorId,
      });

      const lookupItem: ILookupVO = renderRecordVO(res, columns);
      utils.ai.fetchLauncherCommands.invalidate();
      await onSuccess(res, lookupItem, 'CREATE');

      return;
    }

    if (status === 'UPDATE' && updateRecord) {
      const res: RecordRenderVO = await updateRecord?.({
        ...recordDto,
        databaseId: props.nodeId,
        mirrorId: props.mirrorId,
        id: props.recordId,
      } as RecordUpdateDTO);
      const lookupItem: ILookupVO = renderRecordVO(res, columns);
      onSuccess(res, lookupItem, 'UPDATE');
    }
  }, [
    recordDetail,
    props.nodeId,
    props.mirrorId,
    props.recordId,
    status,
    createRecord,
    columns,
    onSuccess,
    updateRecord,
  ]);

  const { sharing } = useShareContext();

  const [commentExpanded] = useLocalStorageState<boolean>('commentExpanded');
  // 是否展开评论区
  const [isCommentExpanded, setRecordExpanded] = useState(
    props.commentExpanded === 'true' || !!commentExpanded,
  );

  useEffect(() => {
    if (window.location.search.includes('commentExpanded=true')) {
      setRecordExpanded(true);
    }
  }, []);

  const { t } = useLocale();

  const handleClose = () => {
    props?.onCancel?.();
  };

  if (isError) {
    return (
      <Stack justifyContent="center" alignItems="center" height="50vh">
        <Empty
          text={t.resource.record_detail.record_detail_not_found}
          src="/assets/images/space_no_permission.png"
          button={
            <Button
              onClick={() => {
                if (props.onClose) {
                  props.onClose();
                }
              }}
            >
              {t.buttons.close}
            </Button>
          }
        />
      </Stack>
    );
  }

  return (
    <Box sx={{ m: -2 }}>
      <SpaceModalConfig showClose={false} />
      {!recordDetail && (
        <Box p={2}>
          <Skeleton pos={'RECORD_DETAIL'} />
        </Box>
      )}
      {recordDetail && (
        <RecordDetailToolbarComponent
          isRecordExpanded={isCommentExpanded}
          setRecordExpanded={setRecordExpanded}
          title={status === 'CREATE' ? t.record.create_record : t.record.record_detail}
          onClose={handleClose}
          status={status}
          handleUpdateStatus={setStatus}
          nodeId={props.mirrorId || props.nodeId!}
          mirrorId={props.mirrorId}
          value={recordDetail}
          onChange={setRecordDetail}
        />
      )}
      {recordDetail && (
        <Stack
          direction={'row'}
          className="flex space-between"
          sx={{
            overflowY: 'hidden',
            height: '80vh',
            ml: 2,
          }}
        >
          <Box
            sx={{
              width:
                props.nodeId && recordDetail?.record?.id && isCommentExpanded
                  ? 'calc(100% - 332px)'
                  : '100%',
            }}
          >
            <RecordDetailVOEditor
              viewId={props.viewId}
              recordId={props.recordId}
              // linkedNodeId={props.linkedNodeId}
              linkedFieldId={props.linkedFieldId}
              databaseId={props.nodeId}
              mirrorId={props.mirrorId}
              spaceId={spaceId}
              status={status}
              onUpdate={refresh}
              setStatus={setStatus}
              value={recordDetail}
              onChange={(data) => {
                setRecordDetail(data);
              }}
            />
            {status !== 'VIEW' && (
              <Box justifyContent={'center'}>
                <FooterButtons
                  disabledConfirm={false}
                  onCancel={handleCancel}
                  confirmLoading={isMutating()}
                  confirmType="submit"
                  onConfirm={async () => {
                    await handleSubmit();
                    updateMutating?.(true);
                  }}
                  confirmText={status === 'UPDATE' ? t.buttons.save : t.buttons.create}
                />
              </Box>
            )}
          </Box>
          {props.nodeId && recordDetail?.record?.id && isCommentExpanded && !sharing && (
            <RecordActivityView
              mirrorId={props.mirrorId}
              databaseId={props.nodeId}
              recordId={recordDetail?.record?.id}
            />
          )}
        </Stack>
      )}
    </Box>
  );
}

export function RecordDetailViewBase(props: Props) {
  console.log('RecordDetailView RecordDetailView', props);
  const { nodeId, mirrorId } = props;
  const trpcQuery = useTRPCQuery();
  const spaceCtx = useSpaceContextForce();
  const { t } = useLocale();

  const id: string | null = useMemo(() => {
    if (mirrorId) {
      return mirrorId;
    }
    if (nodeId) {
      return nodeId;
    }
    return null;
  }, [nodeId, mirrorId]);

  const { data: nodeDetail, isError } = trpcQuery.node.detail.useQuery(
    {
      id: id ?? '',
    },
    {
      enabled: id != null && id.length > 0,
      // staleTime: consts.CONST_QUERY_STALE_TIME,
    },
  );

  const databaseVO: DatabaseVO | null = useMemo(() => {
    if (!nodeDetail) {
      return null;
    }
    if (nodeDetail.type === 'MIRROR') {
      const mirrorResource = nodeDetail.resource as MirrorVO;
      // if (mirrorResource.mirrorType === 'DATABASE_VIEW' || mirrorResource.mirrorType === 'VIEW') {
      //   return {
      //     ...mirrorResource.database,
      //     views: [mirrorResource.view],
      //   };
      // }
      return {
        ...mirrorResource.database,
        views: [mirrorResource.view],
      };
    }

    if (nodeDetail.type === 'DATABASE') {
      return nodeDetail.resource as DatabaseVO;
    }

    return null;
  }, [nodeDetail]);

  if (isError) {
    return (
      <Stack justifyContent="center" alignItems="center" height="50vh">
        <Empty
          text={t.error.space.title}
          description={t.error.node_server_error}
          src="/assets/images/space_no_permission.png"
          button={
            <Button
              onClick={() => {
                spaceCtx.showUIModal(null);
              }}
            >
              {t.buttons.close}
            </Button>
          }
        />
      </Stack>
    );
  }

  return (
    <>
      {databaseVO ? (
        <DatabaseVOProviderWithApi database={databaseVO} databaseViewId={props.viewId}>
          <RecordDetailViewInner {...props} />
        </DatabaseVOProviderWithApi>
      ) : (
        <Box width="70vw" height="100%">
          <Skeleton pos="RECORD_DETAIL" />
        </Box>
      )}
    </>
  );
}

export const RecordDetailView = memo(RecordDetailViewBase);
