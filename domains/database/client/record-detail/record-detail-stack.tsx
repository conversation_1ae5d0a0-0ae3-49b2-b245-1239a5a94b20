import { createStackNavigator } from '@bika/ui/stack-navigator';
import React, { useMemo } from 'react';
import type { IRecordDetailParams, RecordDetailControlStatus } from './interface';
import { LookupRecordContextProvider } from './lookup-record-context';
// eslint-disable-next-line import/no-cycle
import { RecordDetailStackScreen } from './record-detail-stack-screen';
import { RecordSelectStackScreen } from './record-select-stack-screen';
// import { NodeDetailVOProviderWithApi } from '../../../node/client/context/node-detail-vo-provider-with-api';

export type RecordDetailStackScreenType = 'RECORD_DETAIL' | 'RECORD_SELECT' | 'RECORD_CREATE';

interface Props {
  initialStatus?: RecordDetailControlStatus;
  commentExpanded?: string;
  closeAfterUpdated?: boolean;
  recordId?: string;
  databaseId?: string;
  viewId?: string;
  mirrorId?: string;
}

const Stack = createStackNavigator<RecordDetailStackScreenType>();

export function RecordDetailStack(props: Props) {
  const mode: RecordDetailControlStatus =
    props.initialStatus ?? (props.recordId ? 'VIEW' : 'CREATE');
  console.log('RecordDetailStack', props);
  console.log('LookupRecordContextProvider', props);

  const initialRouteParams: IRecordDetailParams<RecordDetailControlStatus> = useMemo(
    () => ({
      mode,
      recordId: props.recordId ?? '',
      commentExpanded: props.commentExpanded ?? '',
      nodeId: props.databaseId ?? '',
      viewId: props.viewId ?? '',
      closeAfterUpdated: props.closeAfterUpdated ?? false,
      mirrorId: props.mirrorId ?? '',
    }),
    [
      mode,
      props.recordId,
      props.commentExpanded,
      props.databaseId,
      props.viewId,
      props.closeAfterUpdated,
      props.mirrorId,
    ],
  );

  const memorized = useMemo(() => {
    return <RecordDetailStackScreen />;
  }, []);

  const memorizedSelect = useMemo(() => {
    return <RecordSelectStackScreen />;
  }, []);

  return (
    <LookupRecordContextProvider>
      <Stack.Navigator
        initialRouteName="RECORD_DETAIL"
        initialRouteParams={initialRouteParams as unknown as Record<string, string>}
      >
        {/* 排第一个的默认push进去 */}
        <Stack.Screen route="RECORD_DETAIL" component={memorized} />
        <Stack.Screen route="RECORD_SELECT" component={memorizedSelect} />
      </Stack.Navigator>
    </LookupRecordContextProvider>
  );
}
