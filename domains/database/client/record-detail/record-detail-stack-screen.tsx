import { useLookupContext } from '@bika/types/database/context';
import type { RecordRenderVO } from '@bika/types/database/vo';
import { useSpaceRouter } from '@bika/types/space/context';
import { useGlobalState } from '@bika/types/website/context';
import type { GridApi } from '@bika/ui/database/types';
import { useStackNavigatorContext } from '@bika/ui/stack-navigator';
import React, { memo, useCallback, useMemo } from 'react';
import { getParams } from './getParams';
import type { IRecordDetailParams, RecordDetailControlStatus } from './interface';
// eslint-disable-next-line import/no-cycle
import { generateStorageKey, RecordDetailView } from './record-detail-view';
import { useRecordDetailNavigation } from './use-record-detail-navigation';
import { useStackNavigateControl } from './use-stack-navigate-control';

export const RecordDetailStackScreen = memo(function RecordDetailStackScreen() {
  const ctx = useStackNavigatorContext();
  const [globalAGGridApi] = useGlobalState<GridApi<RecordRenderVO>>('AG_GRID_API');
  const stackControl = useStackNavigateControl();
  const navigate = useRecordDetailNavigation();
  const { uniqueId } = useLookupContext();
  const router = useSpaceRouter();

  const params = useMemo(
    () => getParams<IRecordDetailParams<RecordDetailControlStatus>>(ctx.params),
    [ctx.params],
  );

  const routerParams = useMemo(() => router?.useParams<{ nodeId: string }>(), [router]);

  const handleSuccess = useCallback(
    (record, reason) => {
      if (reason === 'UPDATE' && params.closeAfterUpdated) {
        globalAGGridApi?.refreshServerSide({ purge: false });
        stackControl.backward();
        return;
      }
      // 如果 打开关联记录页，则不刷新列表
      if (!params.targetRecordId) {
        const hasMirrorId = !!params.mirrorId;
        if (hasMirrorId || routerParams?.nodeId === params.nodeId) {
          globalAGGridApi?.refreshServerSide({ purge: false });
        }
      }
      if (reason === 'CREATE' && !params.targetRecordId) {
        stackControl.backward();
        return;
      }
      if (!params.recordId) {
        navigate.replace({
          recordId: record.id,
          mode: 'VIEW',
        });
      }
    },
    [params, globalAGGridApi, stackControl, routerParams, navigate],
  );

  const handleClose = useCallback(() => {
    stackControl.closeModal();
  }, [stackControl]);

  const handleCancel = useCallback(() => {
    stackControl.closeModal();
  }, [stackControl]);

  if (params.nodeId == null && params.recordId == null) {
    return null;
  }
  console.log('RecordDetailStackScreen', params);

  return (
    <RecordDetailView
      commentExpanded={params.commentExpanded ?? ''}
      nodeId={params.nodeId}
      viewId={params.viewId}
      mirrorId={params.mirrorId}
      targetRecordId={params.targetRecordId ?? ''}
      recordId={params.recordId}
      targetFieldId={params.targetFieldId}
      mode={params.mode}
      //  required due to state update
      key={generateStorageKey(params, uniqueId)}
      linkedFieldId={params.linkedFieldId}
      onSuccess={handleSuccess}
      onClose={handleClose}
      onCancel={handleCancel}
    />
  );
});
