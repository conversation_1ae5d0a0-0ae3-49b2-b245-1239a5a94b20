'use client';

import { APICallerProvider } from '@bika/api-caller/context';
import { LocaleProvider } from '@bika/contents/i18n/context';
import type { Dictionary } from '@bika/contents/i18n/translate';
import { Rewardful } from '@bika/domains/pricing/client/rewardful';
import { MuiProLicense } from '@bika/domains/shared/client/license/index';
import { TrackingCode } from '@bika/domains/shared/client/tracking-code/index';
import { GlobalProvider } from '@bika/domains/website/client/context/global/provider';
import type { ThemeStyle } from '@bika/types/website/bo';
import type { IWebsiteLayoutInitialData } from '@bika/types/website/context';
import { NextUIFrameworkProvider } from '@bika/ui/framework/next-framework-provier';
import type { Locale } from 'basenext/i18n';
import { RootHtml } from 'basenext/website';
import { Inter, Roboto } from 'next/font/google';
import Script from 'next/script';
import type React from 'react';
import toast from 'react-hot-toast';

import '@bika/domains/shared/client/styles/globals.css';
import '@bika/domains/shared/client/styles/markdown.css';
import 'simplebar-react/dist/simplebar.min.css';

const inter = Inter({ subsets: ['latin'] });
// const poppins = Poppins({ weight: ['400', '500', '600'], subsets: ['latin'] });
const roboto = Roboto({ subsets: ['latin'] });

export interface Props {
  children: React.ReactNode;
  headers: Headers;
  initialData: IWebsiteLayoutInitialData;
  dictionary: Dictionary;
  // 服务器？还是纯客户端？ SPA 模式，没有任何路由，都是?pathname=XXX
  mode: 'RSC' | 'SPA';
  basePath?: string;
}

const WebsiteLayoutNextWrapper = (props: Pick<Props, 'initialData' | 'children'>) => {
  const { children } = props;
  const data = props.initialData; // await getInitialData(); // #perfHook
  // const theme = props.themeMode;

  return (
    <RootHtml<Locale, ThemeStyle>
      locale={data.locale}
      themeMode={data.themeMode}
      themeStyle={data.themeStyle}
    >
      <body className={`${inter.className} ${roboto.className}`}>{children}</body>
      {/* 是否开启激励系统Rewardful注入脚本 */}
      {props.initialData.appEnv === 'PRODUCTION' && <Rewardful />}
    </RootHtml>
  );
};

const WebsiteLayoutNextInner = (props: Props) => {
  const data = props.initialData; // await getInitialData(); // #perfHook
  // const theme = props.themeMode;
  const dictionary = props.dictionary; // await getDictionary(data.locale as Locale);
  const h = props.headers;

  return (
    <>
      <NextUIFrameworkProvider
        mode={props.mode}
        initServerData={{
          hostname: data.hostname, // process.env.APP_HOSTNAME || '',
          // Docker环境部署的时候, 不会去预设这个STORAGE_PUBLIC_URL, 没有就使用附件的公开端点位置
          storageHostname: data.servers.storagePublicUrl, // process.env.STORAGE_PUBLIC_URL || AttachmentSO.publicEndpoint(),
          headers: h,
        }}
      >
        <LocaleProvider defaultLocale={data.locale} defaultDictionary={dictionary}>
          {/* TODO: 这个locale没有动态抓locale provider的locale state，仅仅是默认值，切换语言后会潜在BUG */}
          {/* 时区同样存在问题 页面不刷新 API发送的还是旧时区数据 */}
          <APICallerProvider
            headers={h}
            timezone={data?.timeZone}
            toast={toast}
            bikaBasePath={props.basePath}
          >
            <GlobalProvider
              initData={{
                ...data,
                // themeMode: theme,
                headers: h,
                mode: props.mode,
              }}
            >
              {props.children}
            </GlobalProvider>
          </APICallerProvider>
        </LocaleProvider>
      </NextUIFrameworkProvider>
      <TrackingCode appEnv={props.initialData.appEnv} />
      <MuiProLicense />
    </>
  );
};

/**
 * Root Layout,  under client component
 *
 * @param props
 * @returns
 */
export function WebsiteLayoutNext(props: Props) {
  return (
    <WebsiteLayoutNextWrapper {...props}>
      <Script
        crossOrigin="anonymous"
        src="//unpkg.com/react-scan/dist/auto.global.js"
        strategy="beforeInteractive"
      />
      <WebsiteLayoutNextInner {...props} />
    </WebsiteLayoutNextWrapper>
  );
}
