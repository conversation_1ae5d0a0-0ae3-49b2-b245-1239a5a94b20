import { getFooterConfig } from '@bika/contents/config/client/footer/index';
import { RemoteStorageSO } from '@bika/domains/system/server';
import { NextWebPageLayout } from '@bika/domains/website/client/layout/next-web-page-layout';
import { LoginCheckRedirect } from '@bika/domains/website/client/login-check-redirect';
import { ReactScan } from '@bika/domains/website/client/react-scan/index';
import { HOMEPAGE } from '@bika/types/shared/constants';
import type { WebsiteNotificationBarsRemoteStorageProperty } from '@bika/types/system/remote-storage';
import type { Locale } from 'basenext/i18n';
import { headers } from 'next/headers';
import Script from 'next/script';
import type React from 'react';

export const dynamic = 'force-dynamic';
// export const fetchCache = 'force-cache';

interface PageProps {
  params: Promise<{
    lang: Locale;
  }>;
  children: React.ReactNode;
}

export default async function RootLayout(props: PageProps) {
  const params = await props.params;
  const { lang } = params;
  const children = props.children;
  const footer = await getFooterConfig(lang);

  const h = await headers();
  // const { pathname, searchParams } = request.nextUrl;
  // const isMobile = headers.get('user-agent')?.toLowerCase().includes('mobile') ?? false;

  // 网站通知栏，仅在网站，不在 space
  const notificationBars =
    await RemoteStorageSO.getProperty<WebsiteNotificationBarsRemoteStorageProperty>(
      'WEBSITE_NOTIFICATION_BARS',
      'SYSTEM',
    );
  return (
    <NextWebPageLayout
      notificationBars={notificationBars}
      params={params}
      config={{ footer }}
      homepage={h.get(HOMEPAGE)}
    >
      <ReactScan />
      <LoginCheckRedirect />
      {children}
    </NextWebPageLayout>
  );
}
