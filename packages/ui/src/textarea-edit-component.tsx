'use client';

import Textarea, { type TextareaProps } from '@mui/joy/Textarea';
import { useUpdateEffect } from 'ahooks';
import type React from 'react';
import { type FC, memo, useState } from 'react';
import { useCssColor } from './colors';
import { LINEBREAK_PLACEHOLDER } from './text-area-component';

type ISearchInputProps = TextareaProps & {
  placeholder?: string;
  value?: string;
  onChange?: (textValue: string) => void;
  disabled?: boolean;
};

export { Textarea };

export const TextAreaEditComponent: FC<ISearchInputProps> = memo(
  ({ placeholder, onChange, value, disabled, ...props }) => {
    console.log('TextAreaEditComponent', props);

    const [textValue, setText] = useState(() => value?.replaceAll(LINEBREAK_PLACEHOLDER, '\n'));

    const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = event.target.value?.replaceAll(LINEBREAK_PLACEHOLDER, '\n');
      onChange?.(newValue);
      setText(newValue);
    };

    useUpdateEffect(() => {
      setText(value?.replaceAll(LINEBREAK_PLACEHOLDER, '\n'));
    }, [value]);

    return (
      <Textarea
        {...props}
        value={textValue}
        placeholder={placeholder}
        onChange={handleChange}
        disabled={disabled}
        slotProps={{
          textarea: {
            readOnly: disabled,
            disabled,
            sx: {
              lineHeight: '22px',
              fontSize: '14px',
            },
          },
          root: {
            sx: {
              backgroundColor: 'transparent',
              color: 'var(--text-secondary)',
              lineHeight: '22px',
              fontSize: '14px',
              width: '100%',
              background: 'transparent',
              border: 'none',
              boxShadow: 'none',

              // ...({disabled != false ? ({
              // [`&:not(.${textareaClasses.focused})`]: {
              //   [`.${textareaClasses.textarea}`]: {
              //     minHeight: 'fit-content !important', // Ensures the height adjusts automatically, but remains consistent
              //     resize: 'none', // Disable manual resizing
              //   },
              // },

              // [`&.${textareaClasses.focused}`]: {
              //   backgroundColor: 'var(--bg-page)',
              //   [`.${textareaClasses.textarea}`]: {
              //     backgroundColor: 'var(--bg-page)',
              //   },
              // },

              // }): {}})
            },
          },
        }}
        sx={{
          color: 'var(--text-secondary)',
          lineHeight: '22px',
          fontSize: '14px',
          width: '100%',
          background: 'transparent',
          border: 'none',
        }}
        onBlur={(e) => {
          handleChange(e);
        }}
      />
    );
  },
);
