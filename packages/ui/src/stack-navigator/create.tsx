'use client';

import { useDeepCompareEffect } from 'ahooks';
import type React from 'react';
import { createContext, memo, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { ButtonComponent } from '../button-component';
import ChevronLeftOutlined from '../icons/components/chevron_left_outlined';
import { Box } from '../layout-components';
import { useModalAsPageController } from '../modal-as-page';
import { Skeleton } from '../skeleton';
import {
  type HeaderBarBreadcumbs,
  useStackHeaderBar,
} from '../stack-header-bar/stack-header-bar-context';
import { StackNavigatorContext, useStackNavigatorContext } from './context';
import { useScreensReducer } from './screen-reducer';
import type { ScreenProps, StackRouter, StackScreenOptions } from './types';

type IRenderChildren = (screen: React.ReactNode) => React.ReactNode;

export interface NavigatorProps {
  // top Route
  initialRouteName: string;
  initialRouteParams?: Record<string, string>;
  children: React.ReactNode | IRenderChildren;
  onCancel?: () => void;
}

/**
 * Stack的全局内存ID，方便调试
 */
let stackIdGen = 0;

/**
 * T，可以设置type，比如string，number等，你自由地约束stack的key字符串类型
 * @returns
 */
export function createStackNavigator<PATH_TYPE extends string>() {
  // 局部context，用于screen向navigator注册
  interface InternalRegisterContextState {
    registerStackScreen: (_props: ScreenProps<PATH_TYPE>) => void;
    setStackOptions: (_opts: StackScreenOptions) => void;
  }

  const InternalRegisterContext = createContext<InternalRegisterContextState>(null!);

  // <Stack.Screen ...
  const Screen = (props: ScreenProps<PATH_TYPE>) => {
    const registerCtx = useContext(InternalRegisterContext);

    // 只注册一次
    useEffect(() => {
      registerCtx.registerStackScreen(props);
    }, []);
    return <> </>;
  };

  // <Stack.Navigator >，导航器
  const Navigator = memo((props: NavigatorProps) => {
    console.log('Navigator render with props:', props);

    const [screensMap, dispatchScreensMap] = useScreensReducer();
    console.log('Current screensMap:', screensMap);

    const [stack, setStack] = useState<PATH_TYPE[]>([]);
    console.log('Current stack:', stack);

    const stackNavigatorContext = useStackNavigatorContext();
    const modalContext = useModalAsPageController();
    const stackHeaderBar = useStackHeaderBar();

    const pushStack = (name: PATH_TYPE) => {
      console.log('pushStack called with:', name);
      setStack((prevArray) => {
        const newArray = [...prevArray];
        newArray.push(name);
        console.log('New stack after push:', newArray);
        return newArray;
      });
    };

    const registerStackScreen = (newProps: ScreenProps<PATH_TYPE>) => {
      console.log('registerStackScreen called with:', newProps);
      dispatchScreensMap({ type: 'REGISTER_SCREEN', screen: newProps });
    };

    const stackRouter = useMemo<StackRouter>(() => {
      console.log('Recreating stackRouter');
      return {
        history: stack,
        replace: (extraParams?: Record<string, string>) => {
          console.log('replace called with params:', extraParams);
          setStack((prevArray: PATH_TYPE[]) => {
            // ... existing replace logic ...
          });
        },
        push: (name: string, extraParams?: Record<string, string>) => {
          console.log('push called with:', name, extraParams);
          const params = new URLSearchParams(extraParams);
          pushStack(`${name}?${params.toString()}` as PATH_TYPE);
        },
        pop: (extraParams?: Record<string, string>) => {
          console.log('pop called with params:', extraParams);
          if (stack.length > 1) {
            setStack((prevArray) => {
              // ... existing pop logic ...
            });
          } else if (stackNavigatorContext != null && modalContext == null) {
            stackNavigatorContext.router.pop();
          } else {
            props.onCancel?.();
          }
        },
        previous: () => {
          console.log('previous called');
          const lastProp = stack[stack.length - 2];
          return lastProp;
        },
        isTop: () => {
          console.log('isTop called');
          if (stackNavigatorContext != null && modalContext == null) {
            return false;
          }
          if (stack.length > 1) {
            return false;
          }
          return true;
        },
      };
    }, [stack, stackNavigatorContext, modalContext]);

    const current = useMemo(() => {
      console.log('Recalculating current from stack');
      const path = stack?.[stack.length - 1] ?? '';
      const [route, queryString] = path.split('?');
      // ... rest of current calculation ...
    }, [stack]);

    const currentScreenProps = useMemo(() => {
      console.log('Recalculating currentScreenProps');
      const path = stack?.[stack.length - 1] ?? '';
      const [pathName] = path.split('?');
      const gStackProps = screensMap[pathName] ?? (() => <Box>{pathName} Not found</Box>);
      return gStackProps;
    }, [stack, screensMap]);

    const setStackOptions = (opts: StackScreenOptions) => {
      console.log('setStackOptions called with:', opts);
      dispatchScreensMap({
        type: 'SET_OPTIONS',
        route: currentScreenProps.route,
        options: opts,
      });
    };

    useDeepCompareEffect(() => {
      console.log('Header bar effect running');
      const path = stack?.[stack.length - 1] ?? '';
      const [pathName] = path.split('?');
      const currentScreen = screensMap[pathName];
      // ... rest of header bar effect ...
    }, [stack, screensMap]);

    useEffect(() => {
      console.log('Initial route effect running');
      if (!initedRoute.current) {
        // ... rest of initial route effect ...
      }
    }, [props.initialRouteName]);

    const Options = (opts: StackScreenOptions) => {
      console.log('Options component rendered with:', opts);
      const registerCtx = useContext(InternalRegisterContext);
      useEffect(() => {
        registerCtx.setStackOptions(opts);
      }, []);
      return <></>;
    };

    console.log('Navigator rendering with currentScreenProps:', currentScreenProps);

    return (
      <StackNavigatorContext.Provider
        value={{
          router: stackRouter,
          params: current.parameters,
          current: current.route,
          Options,
        }}
      >
        <InternalRegisterContext.Provider value={{ registerStackScreen, setStackOptions }}>
          {typeof props.children === 'function' ? (
            props.children(currentScreenProps?.component)
          ) : (
            <>
              {props.children}
              {currentScreenProps?.component}
              {currentScreenProps?.component == null && <Skeleton pos={'RECORD_DETAIL'} />}
            </>
          )}
        </InternalRegisterContext.Provider>
      </StackNavigatorContext.Provider>
    );
  });

  stackIdGen += 1;
  const id = stackIdGen;

  return {
    id,
    Screen,
    Navigator,
  };
}
